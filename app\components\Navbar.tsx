import React from 'react';
import Button from './Button';
import './Navbar.css';

interface NavbarProps {
  onJoinWaitlist?: () => void;
}

export default function Navbar({ onJoinWaitlist }: NavbarProps) {
  const navItems = [
    { label: 'Home', active: true },
    { label: 'Who we are', active: false },
    { label: 'Contact Us', active: false }
  ];

  return (
    <nav className="navbar">
      <div className="navbar__brand">
        LawVriksh
      </div>
      
      <div className="navbar__content">
        <div className="navbar__nav">
          {navItems.map((item, index) => (
            <div key={item.label} className="navbar__nav-item">
              <span className="navbar__nav-link">
                {item.label}
              </span>
              {item.active && (
                <div className="navbar__nav-dot">
                  <svg width="6" height="6" viewBox="0 0 6 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="3" cy="3" r="3" fill="black" />
                  </svg>
                </div>
              )}
            </div>
          ))}
        </div>
        
        <Button onClick={onJoinWaitlist}>
          Join Waitlist
        </Button>
      </div>
    </nav>
  );
}
