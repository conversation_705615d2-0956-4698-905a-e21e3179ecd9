/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Josefin+Sans:ital,wght@0,400;1,400&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Battambang:wght@400;700&display=swap');

.homepage {
  overflow: hidden;
  position: relative;
  background-color: white;
  height: 871px;
  width: 1920px;
}

.homepage__background-image {
  position: absolute;
  top: 1px;
  left: 1px;
  flex-shrink: 0;
  aspect-ratio: 1917/875;
  height: 875px;
  width: 1917px;
}

.homepage__secondary-image {
  position: absolute;
  top: 112px;
  flex-shrink: 0;
  aspect-ratio: 634/275;
  height: 825px;
  left: 9px;
  width: 1902px;
}

.homepage__hero-title {
  position: absolute;
  flex-shrink: 0;
  font-family: 'Baskerville Old Face', 'Times New Roman', serif;
  font-size: 96px;
  color: #b45309;
  height: 327px;
  line-height: 115.83px;
  left: 67px;
  top: 133px;
  width: 892px;
}

.homepage__features-section {
  display: flex;
  position: absolute;
  left: 56px;
  flex-direction: column;
  gap: 40px;
  align-items: flex-start;
  height: 277px;
  top: 509px;
  width: 310px;
}

.homepage__feature-item {
  display: flex;
  position: relative;
  gap: 20px;
  align-items: center;
}

.homepage__feature-icon {
  position: relative;
  height: 75px;
  width: 59px;
}

.homepage__feature-icon--medium {
  height: 63px;
  width: 59px;
}

.homepage__feature-icon--small {
  height: 59px;
  width: 59px;
}

.homepage__feature-content {
  display: flex;
  position: relative;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
  width: 173px;
}

.homepage__feature-content--wide {
  width: 233px;
}

.homepage__feature-title {
  position: relative;
  align-self: stretch;
  font-family: 'Battambang', sans-serif;
  font-size: 24px;
  letter-spacing: -0.05em;
  line-height: 24px;
  color: #b45309;
}

.homepage__feature-description {
  position: relative;
  align-self: stretch;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 18px;
  letter-spacing: -0.05em;
  line-height: 20px;
  color: black;
}

.homepage__main-section {
  display: flex;
  position: absolute;
  flex-direction: column;
  gap: 80px;
  align-items: flex-start;
  height: 441px;
  left: 1087px;
  top: 346px;
  width: 950px;
}

.homepage__main-title {
  position: relative;
  align-self: stretch;
  font-family: 'Baskerville Old Face', 'Times New Roman', serif;
  font-size: 72px;
  letter-spacing: -0.05em;
  color: black;
  height: 155px;
  line-height: 65.28px;
}

.homepage__main-content {
  display: flex;
  position: relative;
  flex-direction: column;
  gap: 0;
  align-items: flex-start;
  width: 797px;
}

.homepage__main-quote {
  position: relative;
  align-self: stretch;
  font-family: 'Josefin Sans', sans-serif;
  font-size: 32px;
  font-style: italic;
  letter-spacing: -0.05em;
  line-height: 32px;
  color: black;
  height: 209px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .homepage {
    width: 100%;
    height: auto;
    min-height: 100vh;
  }

  .homepage__background-image,
  .homepage__secondary-image {
    position: relative;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
  }

  .homepage__hero-title {
    height: auto;
    font-size: 72px;
    left: 5%;
    top: 150px;
    width: 90%;
  }

  .homepage__features-section {
    height: auto;
    left: 5%;
    top: 400px;
    width: 90%;
  }

  .homepage__main-section {
    height: auto;
    left: 5%;
    top: 700px;
    width: 90%;
  }

  .homepage__main-title {
    height: auto;
    font-size: 48px;
  }

  .homepage__main-content {
    gap: 20px;
    width: 100%;
  }

  .homepage__main-quote {
    height: auto;
    font-size: 24px;
  }
}

@media (max-width: 640px) {
  .homepage__hero-title {
    font-size: 48px;
    top: 120px;
  }

  .homepage__features-section {
    top: 300px;
  }

  .homepage__main-section {
    top: 600px;
  }

  .homepage__main-title {
    font-size: 36px;
  }

  .homepage__main-quote {
    font-size: 18px;
  }

  .homepage__feature-item {
    flex-direction: column;
    text-align: center;
  }
}
