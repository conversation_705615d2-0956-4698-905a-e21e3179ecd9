.button {
  display: flex;
  gap: 10px;
  justify-content: center;
  align-items: center;
  padding: 20px 32px;
  border: none;
  cursor: pointer;
  font-family: 'Merriweather', serif;
  font-weight: bold;
  letter-spacing: 0.05em;
  transition: all 0.2s ease;
}

.button:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.button:active {
  transform: translateY(0);
}

/* Variants */
.button--primary {
  background-color: #b45309; /* yellow-700 equivalent */
  color: white;
}

.button--secondary {
  background-color: transparent;
  color: #b45309;
  border: 2px solid #b45309;
}

/* Sizes */
.button--small {
  padding: 12px 24px;
  font-size: 14px;
  line-height: 16px;
}

.button--medium {
  padding: 20px 32px;
  font-size: 16px;
  line-height: 16px;
  height: 59px;
  min-width: 156px;
}

.button--large {
  padding: 20px 32px;
  font-size: 20px;
  line-height: 24px;
  letter-spacing: 0.1em;
  height: 65px;
  min-width: 206px;
}

/* Responsive */
@media (max-width: 640px) {
  .button--medium,
  .button--large {
    width: 100%;
  }
}
