.navbar {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  position: absolute;
  top: 16px;
  left: 67px;
  width: 1814px;
  height: 59px;
}

.navbar__brand {
  font-family: 'Baskerville Old Face', 'Times New Roman', serif;
  font-size: 36px;
  letter-spacing: -0.05em;
  line-height: 40px;
  color: black;
}

.navbar__content {
  display: flex;
  gap: 128px;
  align-items: center;
}

.navbar__nav {
  position: relative;
  display: flex;
  gap: 0;
  align-items: center;
  height: 23px;
  width: 334px;
}

.navbar__nav-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.navbar__nav-item:not(:last-child) {
  margin-right: 80px;
}

.navbar__nav-link {
  font-size: 20px;
  letter-spacing: 0.1em;
  line-height: 24px;
  color: black;
  text-decoration: none;
  cursor: pointer;
  transition: color 0.2s ease;
}

.navbar__nav-link:hover {
  color: #966F33;
}

.navbar__nav-dot {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .navbar {
    left: 0;
    top: 20px;
    flex-direction: column;
    gap: 20px;
    padding: 0 20px;
    width: 100%;
    height: auto;
  }

  .navbar__content {
    flex-direction: column;
    gap: 20px;
  }

  .navbar__nav {
    display: flex;
    gap: 20px;
    justify-content: center;
    width: 100%;
    height: auto;
  }

  .navbar__nav-item:not(:last-child) {
    margin-right: 0;
  }

  .navbar__brand {
    font-size: 28px;
  }
}

@media (max-width: 640px) {
  .navbar__nav {
    display: none;
  }

  .navbar__content {
    flex-direction: row;
    justify-content: space-between;
  }
}
