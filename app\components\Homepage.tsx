import Navbar from './Navbar';
import Button from './Button';
import './Homepage.css';

export default function Homepage() {
  const handleJoinWaitlist = () => {
    // Handle join waitlist action
    console.log('Join waitlist clicked');
  };

  const features = [
    {
      icon: "https://api.builder.io/api/v1/image/assets/TEMP/aa858a2f8ed0a134930f72e7fef50e364573ebfc?width=118",
      title: "Features",
      description: "Benefit of joining waiting list",
      iconClass: "homepage__feature-icon"
    },
    {
      icon: "https://api.builder.io/api/v1/image/assets/TEMP/9badbe856312fc48be0805e349db264b09d2846c?width=118",
      title: "Why You should Join us",
      description: "Urgency of joining waiting list",
      iconClass: "homepage__feature-icon homepage__feature-icon--medium",
      contentClass: "homepage__feature-content homepage__feature-content--wide"
    },
    {
      icon: "https://api.builder.io/api/v1/image/assets/TEMP/189e56502619bfcd3df1b9c9dfa220747ca30a92?width=118",
      title: "Testimonials",
      description: "Benefit of joining waiting list",
      iconClass: "homepage__feature-icon homepage__feature-icon--small"
    }
  ];

  return (
    <div className="homepage">

      
      <Navbar onJoinWaitlist={handleJoinWaitlist} />
      
      <div className="homepage__hero-title">
        Be a legal entreprenuer
      </div>
      
      <div className="homepage__features-section">
        {features.map((feature, index) => (
          <div key={index} className="homepage__feature-item">
            <img
              src={feature.icon}
              alt=""
              className={feature.iconClass}
            />
            <div className={feature.contentClass || "homepage__feature-content"}>
              <div className="homepage__feature-title">
                {feature.title}
              </div>
              <div className="homepage__feature-description">
                {feature.description}
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="homepage__main-section">
        <div className="homepage__main-title">
          Creator platform for modern --- legal professionals
        </div>
        <div className="homepage__main-content">
          <div className="homepage__main-quote">
            &ldquo;LawVriksh is the first AI-powered platform that helps legal
            experts build a respected online voice, create high-impact content,
            and unlock new monetization opportunities.&rdquo;
          </div>
          <Button size="large" onClick={handleJoinWaitlist}>
            Join Waitlist
          </Button>
        </div>
      </div>
    </div>
  );
}
