import type { MetaFunction } from "@remix-run/node";
import * as React from "react";

export const meta: MetaFunction = () => {
  return [
    { title: "LawVriksh - Legal Entrepreneur Platform" },
    {
      name: "description",
      content:
        "Creator platform for modern legal professionals. Be a legal entrepreneur with LawVriksh.",
    },
  ];
};

export default function Homepage() {
  return (
    <div className="overflow-hidden relative bg-white h-[871px] w-[1920px] max-md:w-full max-md:h-auto max-md:min-h-screen">
      <img
        src=""
        alt=""
        className="absolute top-px left-px shrink-0 aspect-[1917/875] h-[875px] w-[1917px] max-md:relative max-md:top-0 max-md:left-0 max-md:w-full max-md:h-auto"
      />
      <img
        src=""
        alt=""
        className="absolute top-28 shrink-0 aspect-[634/275] h-[825px] left-[9px] w-[1902px] max-md:relative max-md:top-0 max-md:left-0 max-md:w-full max-md:h-auto"
      />
      <div className="flex absolute top-4 justify-between items-end h-[59px] left-[67px] w-[1814px] max-md:left-0 max-md:top-5 max-md:flex-col max-md:gap-5 max-md:px-5 max-md:py-0 max-md:w-full max-md:h-auto max-sm:flex-row max-sm:justify-between">
        <div className="text-4xl tracking-tighter leading-10 text-black max-sm:text-3xl">
          LawVriksh
        </div>
        <div className="flex gap-32 items-center max-md:flex-col max-md:gap-5 max-sm:flex-row">
          <div className="relative h-[23px] w-[334px] max-md:flex max-md:gap-5 max-md:justify-center max-md:w-full max-md:h-auto max-sm:hidden">
            <div className="absolute top-0 text-xl tracking-widest leading-6 text-black h-[23px] max-md:relative max-md:top-auto max-md:left-auto max-md:w-auto max-md:h-auto">
              Home
            </div>
            <div>
              <div
                dangerouslySetInnerHTML={{
                  __html:
                    '<svg id="2170:197" width="6" height="6" viewBox="0 0 6 6" fill="none" xmlns="http://www.w3.org/2000/svg" class="nav-dot" style="width: 6px; height: 6px; flex-shrink: 0; fill: #000; position: absolute; top: 10px"> <circle cx="3" cy="3" r="3" fill="black"></circle> </svg>',
                }}
              />
            </div>
            <div className="absolute top-0 text-xl tracking-widest leading-6 text-black h-[23px] max-md:relative max-md:top-auto max-md:left-auto max-md:w-auto max-md:h-auto">
              Who we are
            </div>
            <div>
              <div
                dangerouslySetInnerHTML={{
                  __html:
                    '<svg id="2170:199" width="6" height="6" viewBox="0 0 6 6" fill="none" xmlns="http://www.w3.org/2000/svg" class="nav-dot" style="width: 6px; height: 6px; flex-shrink: 0; fill: #000; position: absolute; top: 10px"> <circle cx="3" cy="3" r="3" fill="black"></circle> </svg>',
                }}
              />
            </div>
            <div className="absolute top-0 text-xl tracking-widest leading-6 text-black h-[23px] max-md:relative max-md:top-auto max-md:left-auto max-md:w-auto max-md:h-auto">
              Contact Us
            </div>
          </div>
          <div className="flex gap-2.5 justify-center items-center px-8 py-5 bg-yellow-700 h-[59px] w-[156px] max-sm:w-full">
            <div className="text-base font-bold tracking-wide leading-4 text-white">
              Join Waitlist
            </div>
          </div>
        </div>
      </div>
      <div className="absolute shrink-0 text-8xl text-yellow-700 h-[327px] leading-[115.83px] left-[67px] top-[133px] w-[892px] max-md:h-auto max-md:text-6xl max-md:left-[5%] max-md:top-[150px] max-md:w-[90%] max-sm:text-4xl max-sm:top-[120px]">
        Be a legal entreprenuer
      </div>
      <div className="flex absolute left-14 flex-col gap-10 items-start h-[277px] top-[509px] w-[310px] max-md:h-auto max-md:left-[5%] max-md:top-[400px] max-md:w-[90%] max-sm:top-[300px]">
        <div className="flex relative gap-5 items-center max-sm:flex-col max-sm:text-center">
          <img
            src="https://api.builder.io/api/v1/image/assets/TEMP/aa858a2f8ed0a134930f72e7fef50e364573ebfc?width=118"
            alt=""
            className="relative aspect-[58.95/75.00] h-[75px] w-[59px]"
          />
          <div className="flex relative flex-col gap-2.5 items-start w-[173px]">
            <div className="relative self-stretch text-2xl tracking-tighter leading-6 text-yellow-700">
              Features
            </div>
            <div className="relative self-stretch text-lg tracking-tighter leading-5 text-black">
              Benefit of joining waiting list
            </div>
          </div>
        </div>
        <div className="flex relative gap-5 items-center max-sm:flex-col max-sm:text-center">
          <img
            src="https://api.builder.io/api/v1/image/assets/TEMP/9badbe856312fc48be0805e349db264b09d2846c?width=118"
            alt=""
            className="relative aspect-[59/63] h-[63px] w-[59px]"
          />
          <div className="flex relative flex-col gap-2.5 items-start w-[233px]">
            <div className="relative self-stretch text-2xl tracking-tighter leading-6 text-yellow-700">
              Why You should Join us
            </div>
            <div className="relative self-stretch text-lg tracking-tighter leading-5 text-black">
              Urgency of joining waiting list
            </div>
          </div>
        </div>
        <div className="flex relative gap-5 items-center max-sm:flex-col max-sm:text-center">
          <img
            src="https://api.builder.io/api/v1/image/assets/TEMP/189e56502619bfcd3df1b9c9dfa220747ca30a92?width=118"
            alt=""
            className="relative aspect-[1/1] h-[59px] w-[59px]"
          />
          <div className="flex relative flex-col gap-2.5 items-start w-[173px]">
            <div className="relative self-stretch text-2xl tracking-tighter leading-6 text-yellow-700">
              Testimonials
            </div>
            <div className="relative self-stretch text-lg tracking-tighter leading-5 text-black">
              Benefit of joining waiting list
            </div>
          </div>
        </div>
      </div>
      <div className="flex absolute flex-col gap-20 items-start h-[441px] left-[1087px] right-[section] top-[346px] w-[950px] max-md:h-auto max-md:left-[5%] max-md:top-[700px] max-md:w-[90%] max-sm:top-[600px]">
        <div className="relative self-stretch text-6xl tracking-tighter text-black h-[155px] leading-[65.28px] right-[heading] max-md:h-auto max-md:text-4xl max-sm:text-3xl">
          Creator platform for modern --- legal professionals
        </div>
        <div className="flex relative flex-col gap-0 items-start right-[content] w-[797px] max-md:gap-5 max-md:w-full">
          <div className="relative self-stretch text-3xl italic tracking-tighter leading-8 text-black h-[209px] max-md:h-auto max-md:text-2xl max-sm:text-lg">
            &quot;LawVriksh is the first AI-powered platform that helps legal
            experts build a respected online voice, create high-impact content,
            and unlock new monetization opportunities.&quot;
          </div>
          <div className="flex relative gap-2.5 justify-center items-center px-8 py-5 bg-yellow-700 h-[65px] w-[206px] max-sm:w-full">
            <div className="relative text-xl tracking-wider leading-6 text-white">
              Join Waitlist
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
